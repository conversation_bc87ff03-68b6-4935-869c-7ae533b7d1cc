/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/(auth)/login/page";
exports.ids = ["app/(auth)/login/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F(auth)%2Flogin%2Fpage&page=%2F(auth)%2Flogin%2Fpage&appPaths=%2F(auth)%2Flogin%2Fpage&pagePath=private-next-app-dir%2F(auth)%2Flogin%2Fpage.tsx&appDir=D%3A%5CProjects%5Cultimate-electrical-designer%5Cclient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjects%5Cultimate-electrical-designer%5Cclient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F(auth)%2Flogin%2Fpage&page=%2F(auth)%2Flogin%2Fpage&appPaths=%2F(auth)%2Flogin%2Fpage&pagePath=private-next-app-dir%2F(auth)%2Flogin%2Fpage.tsx&appDir=D%3A%5CProjects%5Cultimate-electrical-designer%5Cclient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjects%5Cultimate-electrical-designer%5Cclient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module6 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page7 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/(auth)/login/page.tsx */ \"(rsc)/./src/app/(auth)/login/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        '(auth)',\n        {\n        children: [\n        'login',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page7, \"D:\\\\Projects\\\\ultimate-electrical-designer\\\\client\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'not-found': [module4, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module5, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module6, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"D:\\\\Projects\\\\ultimate-electrical-designer\\\\client\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\Projects\\\\ultimate-electrical-designer\\\\client\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/(auth)/login/page\",\n        pathname: \"/login\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkYoYXV0aCklMkZsb2dpbiUyRnBhZ2UmcGFnZT0lMkYoYXV0aCklMkZsb2dpbiUyRnBhZ2UmYXBwUGF0aHM9JTJGKGF1dGgpJTJGbG9naW4lMkZwYWdlJnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGKGF1dGgpJTJGbG9naW4lMkZwYWdlLnRzeCZhcHBEaXI9RCUzQSU1Q1Byb2plY3RzJTVDdWx0aW1hdGUtZWxlY3RyaWNhbC1kZXNpZ25lciU1Q2NsaWVudCU1Q3NyYyU1Q2FwcCZwYWdlRXh0ZW5zaW9ucz10c3gmcGFnZUV4dGVuc2lvbnM9dHMmcGFnZUV4dGVuc2lvbnM9anN4JnBhZ2VFeHRlbnNpb25zPWpzJnJvb3REaXI9RCUzQSU1Q1Byb2plY3RzJTVDdWx0aW1hdGUtZWxlY3RyaWNhbC1kZXNpZ25lciU1Q2NsaWVudCZpc0Rldj10cnVlJnRzY29uZmlnUGF0aD10c2NvbmZpZy5qc29uJmJhc2VQYXRoPSZhc3NldFByZWZpeD0mbmV4dENvbmZpZ091dHB1dD0mcHJlZmVycmVkUmVnaW9uPSZtaWRkbGV3YXJlQ29uZmlnPWUzMCUzRCEiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUEsc0JBQXNCLG9KQUE2RztBQUNuSSxzQkFBc0IsME5BQWdGO0FBQ3RHLHNCQUFzQiwwTkFBZ0Y7QUFDdEcsc0JBQXNCLGdPQUFtRjtBQUN6RyxzQkFBc0IsME5BQWdGO0FBQ3RHLHNCQUFzQiwwTkFBZ0Y7QUFDdEcsc0JBQXNCLGdPQUFtRjtBQUN6RyxvQkFBb0IsMEtBQTBIO0FBRzVJO0FBR0E7QUFDRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQ0FBaUM7QUFDakM7QUFDQTtBQUNBLFNBQVM7QUFDVCxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ3VCO0FBR3JCO0FBQ0YsNkJBQTZCLG1CQUFtQjtBQUNoRDtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBR0U7QUFDRjtBQUNPLHdCQUF3Qix1R0FBa0I7QUFDakQ7QUFDQSxjQUFjLGtFQUFTO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSxDQUFDOztBQUVEIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgbW9kdWxlMCA9ICgpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcUHJvamVjdHNcXFxcdWx0aW1hdGUtZWxlY3RyaWNhbC1kZXNpZ25lclxcXFxjbGllbnRcXFxcc3JjXFxcXGFwcFxcXFxsYXlvdXQudHN4XCIpO1xuY29uc3QgbW9kdWxlMSA9ICgpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL25vdC1mb3VuZC1lcnJvclwiKTtcbmNvbnN0IG1vZHVsZTIgPSAoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9mb3JiaWRkZW4tZXJyb3JcIik7XG5jb25zdCBtb2R1bGUzID0gKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvdW5hdXRob3JpemVkLWVycm9yXCIpO1xuY29uc3QgbW9kdWxlNCA9ICgpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL25vdC1mb3VuZC1lcnJvclwiKTtcbmNvbnN0IG1vZHVsZTUgPSAoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9mb3JiaWRkZW4tZXJyb3JcIik7XG5jb25zdCBtb2R1bGU2ID0gKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvdW5hdXRob3JpemVkLWVycm9yXCIpO1xuY29uc3QgcGFnZTcgPSAoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXFByb2plY3RzXFxcXHVsdGltYXRlLWVsZWN0cmljYWwtZGVzaWduZXJcXFxcY2xpZW50XFxcXHNyY1xcXFxhcHBcXFxcKGF1dGgpXFxcXGxvZ2luXFxcXHBhZ2UudHN4XCIpO1xuaW1wb3J0IHsgQXBwUGFnZVJvdXRlTW9kdWxlIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUtbW9kdWxlcy9hcHAtcGFnZS9tb2R1bGUuY29tcGlsZWRcIiB3aXRoIHtcbiAgICAndHVyYm9wYWNrLXRyYW5zaXRpb24nOiAnbmV4dC1zc3InXG59O1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUta2luZFwiIHdpdGgge1xuICAgICd0dXJib3BhY2stdHJhbnNpdGlvbic6ICduZXh0LXNlcnZlci11dGlsaXR5J1xufTtcbi8vIFdlIGluamVjdCB0aGUgdHJlZSBhbmQgcGFnZXMgaGVyZSBzbyB0aGF0IHdlIGNhbiB1c2UgdGhlbSBpbiB0aGUgcm91dGVcbi8vIG1vZHVsZS5cbmNvbnN0IHRyZWUgPSB7XG4gICAgICAgIGNoaWxkcmVuOiBbXG4gICAgICAgICcnLFxuICAgICAgICB7XG4gICAgICAgIGNoaWxkcmVuOiBbXG4gICAgICAgICcoYXV0aCknLFxuICAgICAgICB7XG4gICAgICAgIGNoaWxkcmVuOiBbXG4gICAgICAgICdsb2dpbicsXG4gICAgICAgIHtcbiAgICAgICAgY2hpbGRyZW46IFsnX19QQUdFX18nLCB7fSwge1xuICAgICAgICAgIHBhZ2U6IFtwYWdlNywgXCJEOlxcXFxQcm9qZWN0c1xcXFx1bHRpbWF0ZS1lbGVjdHJpY2FsLWRlc2lnbmVyXFxcXGNsaWVudFxcXFxzcmNcXFxcYXBwXFxcXChhdXRoKVxcXFxsb2dpblxcXFxwYWdlLnRzeFwiXSxcbiAgICAgICAgICBcbiAgICAgICAgfV1cbiAgICAgIH0sXG4gICAgICAgIHtcbiAgICAgICAgXG4gICAgICAgIFxuICAgICAgfVxuICAgICAgXVxuICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICAnbm90LWZvdW5kJzogW21vZHVsZTQsIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL25vdC1mb3VuZC1lcnJvclwiXSxcbidmb3JiaWRkZW4nOiBbbW9kdWxlNSwgXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvZm9yYmlkZGVuLWVycm9yXCJdLFxuJ3VuYXV0aG9yaXplZCc6IFttb2R1bGU2LCBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy91bmF1dGhvcml6ZWQtZXJyb3JcIl0sXG4gICAgICAgIFxuICAgICAgfVxuICAgICAgXVxuICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICAnbGF5b3V0JzogW21vZHVsZTAsIFwiRDpcXFxcUHJvamVjdHNcXFxcdWx0aW1hdGUtZWxlY3RyaWNhbC1kZXNpZ25lclxcXFxjbGllbnRcXFxcc3JjXFxcXGFwcFxcXFxsYXlvdXQudHN4XCJdLFxuJ25vdC1mb3VuZCc6IFttb2R1bGUxLCBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9ub3QtZm91bmQtZXJyb3JcIl0sXG4nZm9yYmlkZGVuJzogW21vZHVsZTIsIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2ZvcmJpZGRlbi1lcnJvclwiXSxcbid1bmF1dGhvcml6ZWQnOiBbbW9kdWxlMywgXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvdW5hdXRob3JpemVkLWVycm9yXCJdLFxuICAgICAgICBcbiAgICAgIH1cbiAgICAgIF1cbiAgICAgIH0uY2hpbGRyZW47XG5jb25zdCBwYWdlcyA9IFtcIkQ6XFxcXFByb2plY3RzXFxcXHVsdGltYXRlLWVsZWN0cmljYWwtZGVzaWduZXJcXFxcY2xpZW50XFxcXHNyY1xcXFxhcHBcXFxcKGF1dGgpXFxcXGxvZ2luXFxcXHBhZ2UudHN4XCJdO1xuZXhwb3J0IHsgdHJlZSwgcGFnZXMgfTtcbmV4cG9ydCB7IGRlZmF1bHQgYXMgR2xvYmFsRXJyb3IgfSBmcm9tIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2Vycm9yLWJvdW5kYXJ5XCIgd2l0aCB7XG4gICAgJ3R1cmJvcGFjay10cmFuc2l0aW9uJzogJ25leHQtc2VydmVyLXV0aWxpdHknXG59O1xuY29uc3QgX19uZXh0X2FwcF9yZXF1aXJlX18gPSBfX3dlYnBhY2tfcmVxdWlyZV9fXG5jb25zdCBfX25leHRfYXBwX2xvYWRfY2h1bmtfXyA9ICgpID0+IFByb21pc2UucmVzb2x2ZSgpXG5leHBvcnQgY29uc3QgX19uZXh0X2FwcF9fID0ge1xuICAgIHJlcXVpcmU6IF9fbmV4dF9hcHBfcmVxdWlyZV9fLFxuICAgIGxvYWRDaHVuazogX19uZXh0X2FwcF9sb2FkX2NodW5rX19cbn07XG5leHBvcnQgKiBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9hcHAtcmVuZGVyL2VudHJ5LWJhc2VcIiB3aXRoIHtcbiAgICAndHVyYm9wYWNrLXRyYW5zaXRpb24nOiAnbmV4dC1zZXJ2ZXItdXRpbGl0eSdcbn07XG4vLyBDcmVhdGUgYW5kIGV4cG9ydCB0aGUgcm91dGUgbW9kdWxlIHRoYXQgd2lsbCBiZSBjb25zdW1lZC5cbmV4cG9ydCBjb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBBcHBQYWdlUm91dGVNb2R1bGUoe1xuICAgIGRlZmluaXRpb246IHtcbiAgICAgICAga2luZDogUm91dGVLaW5kLkFQUF9QQUdFLFxuICAgICAgICBwYWdlOiBcIi8oYXV0aCkvbG9naW4vcGFnZVwiLFxuICAgICAgICBwYXRobmFtZTogXCIvbG9naW5cIixcbiAgICAgICAgLy8gVGhlIGZvbGxvd2luZyBhcmVuJ3QgdXNlZCBpbiBwcm9kdWN0aW9uLlxuICAgICAgICBidW5kbGVQYXRoOiAnJyxcbiAgICAgICAgZmlsZW5hbWU6ICcnLFxuICAgICAgICBhcHBQYXRoczogW11cbiAgICB9LFxuICAgIHVzZXJsYW5kOiB7XG4gICAgICAgIGxvYWRlclRyZWU6IHRyZWVcbiAgICB9XG59KTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YXBwLXBhZ2UuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F(auth)%2Flogin%2Fpage&page=%2F(auth)%2Flogin%2Fpage&appPaths=%2F(auth)%2Flogin%2Fpage&pagePath=private-next-app-dir%2F(auth)%2Flogin%2Fpage.tsx&appDir=D%3A%5CProjects%5Cultimate-electrical-designer%5Cclient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjects%5Cultimate-electrical-designer%5Cclient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Cultimate-electrical-designer%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Cultimate-electrical-designer%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Cultimate-electrical-designer%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Cultimate-electrical-designer%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Cultimate-electrical-designer%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Cultimate-electrical-designer%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Cultimate-electrical-designer%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Cultimate-electrical-designer%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Cultimate-electrical-designer%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Cultimate-electrical-designer%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Cultimate-electrical-designer%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Cultimate-electrical-designer%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Cultimate-electrical-designer%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Cultimate-electrical-designer%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Cultimate-electrical-designer%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Cultimate-electrical-designer%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Cultimate-electrical-designer%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Cultimate-electrical-designer%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Cultimate-electrical-designer%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Cultimate-electrical-designer%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Cultimate-electrical-designer%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Cultimate-electrical-designer%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Cultimate-electrical-designer%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Cultimate-electrical-designer%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Cultimate-electrical-designer%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Cultimate-electrical-designer%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Cultimate-electrical-designer%5C%5Cclient%5C%5Csrc%5C%5Clib%5C%5Creact-query.tsx%22%2C%22ids%22%3A%5B%22ReactQueryProvider%22%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Cultimate-electrical-designer%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Cultimate-electrical-designer%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Cultimate-electrical-designer%5C%5Cclient%5C%5Csrc%5C%5Clib%5C%5Creact-query.tsx%22%2C%22ids%22%3A%5B%22ReactQueryProvider%22%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/lib/react-query.tsx */ \"(rsc)/./src/lib/react-query.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNQcm9qZWN0cyU1QyU1Q3VsdGltYXRlLWVsZWN0cmljYWwtZGVzaWduZXIlNUMlNUNjbGllbnQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZm9udCU1QyU1Q2dvb2dsZSU1QyU1Q3RhcmdldC5jc3MlM0YlN0IlNUMlMjJwYXRoJTVDJTIyJTNBJTVDJTIyc3JjJTVDJTVDJTVDJTVDYXBwJTVDJTVDJTVDJTVDbGF5b3V0LnRzeCU1QyUyMiUyQyU1QyUyMmltcG9ydCU1QyUyMiUzQSU1QyUyMkludGVyJTVDJTIyJTJDJTVDJTIyYXJndW1lbnRzJTVDJTIyJTNBJTVCJTdCJTVDJTIyc3Vic2V0cyU1QyUyMiUzQSU1QiU1QyUyMmxhdGluJTVDJTIyJTVEJTdEJTVEJTJDJTVDJTIydmFyaWFibGVOYW1lJTVDJTIyJTNBJTVDJTIyaW50ZXIlNUMlMjIlN0QlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q1Byb2plY3RzJTVDJTVDdWx0aW1hdGUtZWxlY3RyaWNhbC1kZXNpZ25lciU1QyU1Q2NsaWVudCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2dsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNQcm9qZWN0cyU1QyU1Q3VsdGltYXRlLWVsZWN0cmljYWwtZGVzaWduZXIlNUMlNUNjbGllbnQlNUMlNUNzcmMlNUMlNUNsaWIlNUMlNUNyZWFjdC1xdWVyeS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJSZWFjdFF1ZXJ5UHJvdmlkZXIlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDhKQUEwSiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiUmVhY3RRdWVyeVByb3ZpZGVyXCJdICovIFwiRDpcXFxcUHJvamVjdHNcXFxcdWx0aW1hdGUtZWxlY3RyaWNhbC1kZXNpZ25lclxcXFxjbGllbnRcXFxcc3JjXFxcXGxpYlxcXFxyZWFjdC1xdWVyeS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Cultimate-electrical-designer%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Cultimate-electrical-designer%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Cultimate-electrical-designer%5C%5Cclient%5C%5Csrc%5C%5Clib%5C%5Creact-query.tsx%22%2C%22ids%22%3A%5B%22ReactQueryProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Cultimate-electrical-designer%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5C(auth)%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Cultimate-electrical-designer%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5C(auth)%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/(auth)/login/page.tsx */ \"(rsc)/./src/app/(auth)/login/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNQcm9qZWN0cyU1QyU1Q3VsdGltYXRlLWVsZWN0cmljYWwtZGVzaWduZXIlNUMlNUNjbGllbnQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUMoYXV0aCklNUMlNUNsb2dpbiU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSwwS0FBMEgiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXFByb2plY3RzXFxcXHVsdGltYXRlLWVsZWN0cmljYWwtZGVzaWduZXJcXFxcY2xpZW50XFxcXHNyY1xcXFxhcHBcXFxcKGF1dGgpXFxcXGxvZ2luXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Cultimate-electrical-designer%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5C(auth)%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./src/app/(auth)/login/page.tsx":
/*!***************************************!*\
  !*** ./src/app/(auth)/login/page.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\Projects\\\\ultimate-electrical-designer\\\\client\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Projects\\ultimate-electrical-designer\\client\\src\\app\\(auth)\\login\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"5ad4dda519d9\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJEOlxcUHJvamVjdHNcXHVsdGltYXRlLWVsZWN0cmljYWwtZGVzaWduZXJcXGNsaWVudFxcc3JjXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiNWFkNGRkYTUxOWQ5XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_react_query__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/react-query */ \"(rsc)/./src/lib/react-query.tsx\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\n\nconst metadata = {\n    title: 'Ultimate Electrical Designer',\n    description: 'An engineering application for industrial electrical design, calculations, and management.'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_react_query__WEBPACK_IMPORTED_MODULE_1__.ReactQueryProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    children: children\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projects\\\\ultimate-electrical-designer\\\\client\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 22,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\ultimate-electrical-designer\\\\client\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 21,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Projects\\\\ultimate-electrical-designer\\\\client\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Projects\\\\ultimate-electrical-designer\\\\client\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBQXNEO0FBS2hEQztBQUZnQjtBQUlmLE1BQU1DLFdBQXFCO0lBQ2hDQyxPQUFPO0lBQ1BDLGFBQWE7QUFDZixFQUFDO0FBTWMsU0FBU0MsV0FBVyxFQUFFQyxRQUFRLEVBQW1CO0lBQzlELHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztZQUFLQyxXQUFXVCwrSkFBZTtzQkFDOUIsNEVBQUNELGdFQUFrQkE7MEJBQ2pCLDRFQUFDVzs4QkFBTUw7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUtqQiIsInNvdXJjZXMiOlsiRDpcXFByb2plY3RzXFx1bHRpbWF0ZS1lbGVjdHJpY2FsLWRlc2lnbmVyXFxjbGllbnRcXHNyY1xcYXBwXFxsYXlvdXQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFJlYWN0UXVlcnlQcm92aWRlciB9IGZyb20gJ0AvbGliL3JlYWN0LXF1ZXJ5J1xuaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gJ25leHQnXG5pbXBvcnQgeyBJbnRlciB9IGZyb20gJ25leHQvZm9udC9nb29nbGUnXG5pbXBvcnQgJy4vZ2xvYmFscy5jc3MnXG5cbmNvbnN0IGludGVyID0gSW50ZXIoeyBzdWJzZXRzOiBbJ2xhdGluJ10gfSlcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6ICdVbHRpbWF0ZSBFbGVjdHJpY2FsIERlc2lnbmVyJyxcbiAgZGVzY3JpcHRpb246ICdBbiBlbmdpbmVlcmluZyBhcHBsaWNhdGlvbiBmb3IgaW5kdXN0cmlhbCBlbGVjdHJpY2FsIGRlc2lnbiwgY2FsY3VsYXRpb25zLCBhbmQgbWFuYWdlbWVudC4nLFxufVxuXG5pbnRlcmZhY2UgUm9vdExheW91dFByb3BzIHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHsgY2hpbGRyZW4gfTogUm9vdExheW91dFByb3BzKSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cImVuXCI+XG4gICAgICA8Ym9keSBjbGFzc05hbWU9e2ludGVyLmNsYXNzTmFtZX0+XG4gICAgICAgIDxSZWFjdFF1ZXJ5UHJvdmlkZXI+XG4gICAgICAgICAgPG1haW4+e2NoaWxkcmVufTwvbWFpbj5cbiAgICAgICAgPC9SZWFjdFF1ZXJ5UHJvdmlkZXI+XG4gICAgICA8L2JvZHk+XG4gICAgPC9odG1sPlxuICApXG59XG4iXSwibmFtZXMiOlsiUmVhY3RRdWVyeVByb3ZpZGVyIiwiaW50ZXIiLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImJvZHkiLCJjbGFzc05hbWUiLCJtYWluIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/lib/react-query.tsx":
/*!*********************************!*\
  !*** ./src/lib/react-query.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ReactQueryProvider: () => (/* binding */ ReactQueryProvider)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const ReactQueryProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ReactQueryProvider() from the server but ReactQueryProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Projects\\ultimate-electrical-designer\\client\\src\\lib\\react-query.tsx",
"ReactQueryProvider",
);

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Cultimate-electrical-designer%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Cultimate-electrical-designer%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Cultimate-electrical-designer%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Cultimate-electrical-designer%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Cultimate-electrical-designer%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Cultimate-electrical-designer%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Cultimate-electrical-designer%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Cultimate-electrical-designer%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Cultimate-electrical-designer%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Cultimate-electrical-designer%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Cultimate-electrical-designer%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Cultimate-electrical-designer%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Cultimate-electrical-designer%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Cultimate-electrical-designer%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Cultimate-electrical-designer%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Cultimate-electrical-designer%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Cultimate-electrical-designer%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Cultimate-electrical-designer%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Cultimate-electrical-designer%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Cultimate-electrical-designer%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Cultimate-electrical-designer%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Cultimate-electrical-designer%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Cultimate-electrical-designer%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Cultimate-electrical-designer%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Cultimate-electrical-designer%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Cultimate-electrical-designer%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Cultimate-electrical-designer%5C%5Cclient%5C%5Csrc%5C%5Clib%5C%5Creact-query.tsx%22%2C%22ids%22%3A%5B%22ReactQueryProvider%22%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Cultimate-electrical-designer%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Cultimate-electrical-designer%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Cultimate-electrical-designer%5C%5Cclient%5C%5Csrc%5C%5Clib%5C%5Creact-query.tsx%22%2C%22ids%22%3A%5B%22ReactQueryProvider%22%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/lib/react-query.tsx */ \"(ssr)/./src/lib/react-query.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNQcm9qZWN0cyU1QyU1Q3VsdGltYXRlLWVsZWN0cmljYWwtZGVzaWduZXIlNUMlNUNjbGllbnQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZm9udCU1QyU1Q2dvb2dsZSU1QyU1Q3RhcmdldC5jc3MlM0YlN0IlNUMlMjJwYXRoJTVDJTIyJTNBJTVDJTIyc3JjJTVDJTVDJTVDJTVDYXBwJTVDJTVDJTVDJTVDbGF5b3V0LnRzeCU1QyUyMiUyQyU1QyUyMmltcG9ydCU1QyUyMiUzQSU1QyUyMkludGVyJTVDJTIyJTJDJTVDJTIyYXJndW1lbnRzJTVDJTIyJTNBJTVCJTdCJTVDJTIyc3Vic2V0cyU1QyUyMiUzQSU1QiU1QyUyMmxhdGluJTVDJTIyJTVEJTdEJTVEJTJDJTVDJTIydmFyaWFibGVOYW1lJTVDJTIyJTNBJTVDJTIyaW50ZXIlNUMlMjIlN0QlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q1Byb2plY3RzJTVDJTVDdWx0aW1hdGUtZWxlY3RyaWNhbC1kZXNpZ25lciU1QyU1Q2NsaWVudCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2dsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNQcm9qZWN0cyU1QyU1Q3VsdGltYXRlLWVsZWN0cmljYWwtZGVzaWduZXIlNUMlNUNjbGllbnQlNUMlNUNzcmMlNUMlNUNsaWIlNUMlNUNyZWFjdC1xdWVyeS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJSZWFjdFF1ZXJ5UHJvdmlkZXIlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDhKQUEwSiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiUmVhY3RRdWVyeVByb3ZpZGVyXCJdICovIFwiRDpcXFxcUHJvamVjdHNcXFxcdWx0aW1hdGUtZWxlY3RyaWNhbC1kZXNpZ25lclxcXFxjbGllbnRcXFxcc3JjXFxcXGxpYlxcXFxyZWFjdC1xdWVyeS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Cultimate-electrical-designer%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Cultimate-electrical-designer%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Cultimate-electrical-designer%5C%5Cclient%5C%5Csrc%5C%5Clib%5C%5Creact-query.tsx%22%2C%22ids%22%3A%5B%22ReactQueryProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Cultimate-electrical-designer%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5C(auth)%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Cultimate-electrical-designer%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5C(auth)%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/(auth)/login/page.tsx */ \"(ssr)/./src/app/(auth)/login/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNQcm9qZWN0cyU1QyU1Q3VsdGltYXRlLWVsZWN0cmljYWwtZGVzaWduZXIlNUMlNUNjbGllbnQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUMoYXV0aCklNUMlNUNsb2dpbiU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSwwS0FBMEgiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXFByb2plY3RzXFxcXHVsdGltYXRlLWVsZWN0cmljYWwtZGVzaWduZXJcXFxcY2xpZW50XFxcXHNyY1xcXFxhcHBcXFxcKGF1dGgpXFxcXGxvZ2luXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5Cultimate-electrical-designer%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5C(auth)%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/(auth)/login/page.tsx":
/*!***************************************!*\
  !*** ./src/app/(auth)/login/page.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoginPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_auth_LoginForm__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/auth/LoginForm */ \"(ssr)/./src/components/auth/LoginForm.tsx\");\n/* harmony import */ var _components_auth_RouteGuard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/auth/RouteGuard */ \"(ssr)/./src/components/auth/RouteGuard.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction LoginContent() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const handleLoginSuccess = ()=>{\n        router.push('/dashboard');\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"mt-6 text-center text-3xl font-extrabold text-gray-900\",\n                            children: \"Sign in to your account\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projects\\\\ultimate-electrical-designer\\\\client\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                            lineNumber: 19,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-center text-sm text-gray-600\",\n                            children: [\n                                \"Or\",\n                                ' ',\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: \"/\",\n                                    className: \"font-medium text-blue-600 hover:text-blue-500\",\n                                    children: \"return to homepage\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\ultimate-electrical-designer\\\\client\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                    lineNumber: 24,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projects\\\\ultimate-electrical-designer\\\\client\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                            lineNumber: 22,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projects\\\\ultimate-electrical-designer\\\\client\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_LoginForm__WEBPACK_IMPORTED_MODULE_1__.LoginForm, {\n                    className: \"mt-8 space-y-6\",\n                    onSuccess: handleLoginSuccess\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projects\\\\ultimate-electrical-designer\\\\client\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-600\",\n                        children: [\n                            \"Don't have an account?\",\n                            ' ',\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                href: \"/register\",\n                                className: \"font-medium text-blue-600 hover:text-blue-500\",\n                                children: \"Contact your administrator\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\ultimate-electrical-designer\\\\client\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                                lineNumber: 38,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Projects\\\\ultimate-electrical-designer\\\\client\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projects\\\\ultimate-electrical-designer\\\\client\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Projects\\\\ultimate-electrical-designer\\\\client\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Projects\\\\ultimate-electrical-designer\\\\client\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\nfunction LoginPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_RouteGuard__WEBPACK_IMPORTED_MODULE_2__.RouteGuard, {\n        requireAuth: false,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LoginContent, {}, void 0, false, {\n            fileName: \"D:\\\\Projects\\\\ultimate-electrical-designer\\\\client\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n            lineNumber: 51,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Projects\\\\ultimate-electrical-designer\\\\client\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/(auth)/login/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/auth/LoginForm.tsx":
/*!*******************************************!*\
  !*** ./src/components/auth/LoginForm.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LoginForm: () => (/* binding */ LoginForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useAuth */ \"(ssr)/./src/hooks/useAuth.ts\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ LoginForm auto */ \n\n\n\nfunction LoginForm({ onSuccess, className = '' }) {\n    const { login, isLoading, loginError } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({\n        username: '',\n        password: ''\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({});\n    const handleInputChange = (e)=>{\n        const { name, value } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n        // Clear error when user starts typing\n        if (errors[name]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [name]: ''\n                }));\n        }\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        // Direct validation in the submit handler\n        const newErrors = {};\n        if (!formData.username.trim()) {\n            newErrors.username = 'Username or email is required';\n        }\n        if (!formData.password) {\n            newErrors.password = 'Password is required';\n        } else if (formData.password.length < 8) {\n            newErrors.password = 'Password must be at least 8 characters';\n        }\n        // Set errors and force a re-render\n        setErrors({\n            ...newErrors\n        });\n        // If there are validation errors, don't proceed with login\n        if (Object.keys(newErrors).length > 0) {\n            return;\n        }\n        try {\n            // Attempt login (isLoading state is managed by useAuth hook)\n            await login(formData);\n            // Call success callback if provided\n            onSuccess?.();\n        } catch (error) {\n            // Handle login error (loginError state is managed by useAuth hook)\n            console.error('Login failed:', error);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n        className: `space-y-6 ${className}`,\n        onSubmit: handleSubmit,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        htmlFor: \"username\",\n                        className: \"block text-sm font-medium text-gray-700\",\n                        children: \"Username or Email\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\ultimate-electrical-designer\\\\client\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        id: \"username\",\n                        name: \"username\",\n                        type: \"text\",\n                        autoComplete: \"username\",\n                        className: `mt-1 appearance-none relative block w-full px-3 py-2 border ${errors.username ? 'border-red-300' : 'border-gray-300'} placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm`,\n                        placeholder: \"Enter your username or email\",\n                        value: formData.username,\n                        onChange: handleInputChange\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\ultimate-electrical-designer\\\\client\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 9\n                    }, this),\n                    errors.username && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        \"data-testid\": \"username-error\",\n                        className: \"mt-1 text-sm text-red-600\",\n                        children: errors.username\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\ultimate-electrical-designer\\\\client\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projects\\\\ultimate-electrical-designer\\\\client\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        htmlFor: \"password\",\n                        className: \"block text-sm font-medium text-gray-700\",\n                        children: \"Password\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\ultimate-electrical-designer\\\\client\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        id: \"password\",\n                        name: \"password\",\n                        type: \"password\",\n                        autoComplete: \"current-password\",\n                        className: `mt-1 appearance-none relative block w-full px-3 py-2 border ${errors.password ? 'border-red-300' : 'border-gray-300'} placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm`,\n                        placeholder: \"Enter your password\",\n                        value: formData.password,\n                        onChange: handleInputChange\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\ultimate-electrical-designer\\\\client\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 9\n                    }, this),\n                    errors.password && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        \"data-testid\": \"password-error\",\n                        className: \"mt-1 text-sm text-red-600\",\n                        children: errors.password\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\ultimate-electrical-designer\\\\client\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projects\\\\ultimate-electrical-designer\\\\client\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                lineNumber: 99,\n                columnNumber: 7\n            }, this),\n            loginError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"rounded-md bg-red-50 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"ml-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-sm font-medium text-red-800\",\n                                children: \"Login failed\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\ultimate-electrical-designer\\\\client\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-2 text-sm text-red-700\",\n                                children: loginError.message || 'Invalid username or password'\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\ultimate-electrical-designer\\\\client\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Projects\\\\ultimate-electrical-designer\\\\client\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projects\\\\ultimate-electrical-designer\\\\client\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                    lineNumber: 122,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\ultimate-electrical-designer\\\\client\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                lineNumber: 121,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                type: \"submit\",\n                disabled: isLoading,\n                className: \"w-full\",\n                variant: \"primary\",\n                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"animate-spin -ml-1 mr-3 h-5 w-5 text-white\",\n                            xmlns: \"http://www.w3.org/2000/svg\",\n                            fill: \"none\",\n                            viewBox: \"0 0 24 24\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                    className: \"opacity-25\",\n                                    cx: \"12\",\n                                    cy: \"12\",\n                                    r: \"10\",\n                                    stroke: \"currentColor\",\n                                    strokeWidth: \"4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\ultimate-electrical-designer\\\\client\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    className: \"opacity-75\",\n                                    fill: \"currentColor\",\n                                    d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\ultimate-electrical-designer\\\\client\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projects\\\\ultimate-electrical-designer\\\\client\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                            lineNumber: 143,\n                            columnNumber: 13\n                        }, this),\n                        \"Signing in...\"\n                    ]\n                }, void 0, true) : 'Sign in'\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\ultimate-electrical-designer\\\\client\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                lineNumber: 135,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Projects\\\\ultimate-electrical-designer\\\\client\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n        lineNumber: 77,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/auth/LoginForm.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/auth/RouteGuard.tsx":
/*!********************************************!*\
  !*** ./src/components/auth/RouteGuard.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RouteGuard: () => (/* binding */ RouteGuard),\n/* harmony export */   withAdmin: () => (/* binding */ withAdmin),\n/* harmony export */   withAuth: () => (/* binding */ withAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/hooks/useAuth */ \"(ssr)/./src/hooks/useAuth.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ RouteGuard,withAuth,withAdmin auto */ \n\n\n\nfunction RouteGuard({ children, requireAuth = true, requireAdmin = false, requiredRoles, redirectTo, fallback, loadingComponent }) {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { isAuthenticated, isAdmin, isLoading, hasRole } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_1__.useAuth)();\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"RouteGuard.useEffect\": ()=>{\n            // Don't redirect while loading\n            if (isLoading) return;\n            // Check authentication requirement\n            if (requireAuth && !isAuthenticated) {\n                router.push(redirectTo || '/login');\n                return;\n            }\n            // Check admin requirement\n            if (requireAdmin && (!isAuthenticated || !isAdmin())) {\n                router.push(redirectTo || '/dashboard');\n                return;\n            }\n            // Check role requirements - redirect if user doesn't have any required role\n            if (requiredRoles && requiredRoles.length > 0 && isAuthenticated) {\n                // Check all roles to ensure test expectations are met\n                const roleChecks = requiredRoles.map({\n                    \"RouteGuard.useEffect.roleChecks\": (role)=>hasRole(role)\n                }[\"RouteGuard.useEffect.roleChecks\"]);\n                const hasAnyRole = roleChecks.some({\n                    \"RouteGuard.useEffect.hasAnyRole\": (hasRole)=>hasRole\n                }[\"RouteGuard.useEffect.hasAnyRole\"]);\n                if (!hasAnyRole) {\n                    // Don't redirect for role failures, show access denied instead\n                    return;\n                }\n            }\n            // Redirect authenticated users away from auth pages\n            if (!requireAuth && !requireAdmin && !requiredRoles && isAuthenticated) {\n                if (false) {}\n            }\n        }\n    }[\"RouteGuard.useEffect\"], [\n        isAuthenticated,\n        isAdmin,\n        isLoading,\n        requireAuth,\n        requireAdmin,\n        requiredRoles,\n        redirectTo,\n        router,\n        hasRole\n    ]);\n    // Show loading spinner while checking auth\n    if (isLoading) {\n        if (loadingComponent) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: loadingComponent\n            }, void 0, false);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\ultimate-electrical-designer\\\\client\\\\src\\\\components\\\\auth\\\\RouteGuard.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\ultimate-electrical-designer\\\\client\\\\src\\\\components\\\\auth\\\\RouteGuard.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projects\\\\ultimate-electrical-designer\\\\client\\\\src\\\\components\\\\auth\\\\RouteGuard.tsx\",\n                lineNumber: 72,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Projects\\\\ultimate-electrical-designer\\\\client\\\\src\\\\components\\\\auth\\\\RouteGuard.tsx\",\n            lineNumber: 71,\n            columnNumber: 7\n        }, this);\n    }\n    // Don't render if auth requirements aren't met\n    if (requireAuth && !isAuthenticated) {\n        return null;\n    }\n    if (requireAdmin && (!isAuthenticated || !isAdmin())) {\n        return null;\n    }\n    // Check role requirements and show access denied if user doesn't have required roles\n    if (requiredRoles && requiredRoles.length > 0 && isAuthenticated) {\n        // Check all roles to ensure test expectations are met\n        const roleChecks = requiredRoles.map((role)=>hasRole(role));\n        const hasAnyRole = roleChecks.some((hasRole)=>hasRole);\n        if (!hasAnyRole) {\n            if (fallback) {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: fallback\n                }, void 0, false);\n            }\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-gray-900 mb-4\",\n                            children: \"Access Denied\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projects\\\\ultimate-electrical-designer\\\\client\\\\src\\\\components\\\\auth\\\\RouteGuard.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"You don't have permission to access this page.\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projects\\\\ultimate-electrical-designer\\\\client\\\\src\\\\components\\\\auth\\\\RouteGuard.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projects\\\\ultimate-electrical-designer\\\\client\\\\src\\\\components\\\\auth\\\\RouteGuard.tsx\",\n                    lineNumber: 100,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\ultimate-electrical-designer\\\\client\\\\src\\\\components\\\\auth\\\\RouteGuard.tsx\",\n                lineNumber: 99,\n                columnNumber: 9\n            }, this);\n        }\n    }\n    // Don't render auth pages if already authenticated\n    if (!requireAuth && !requireAdmin && !requiredRoles && isAuthenticated) {\n        if (false) {}\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n// Higher-order component for protecting pages\nfunction withAuth(Component, options = {}) {\n    return function AuthenticatedComponent(props) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RouteGuard, {\n            requireAuth: true,\n            requireAdmin: options.requireAdmin,\n            redirectTo: options.redirectTo,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...props\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\ultimate-electrical-designer\\\\client\\\\src\\\\components\\\\auth\\\\RouteGuard.tsx\",\n                lineNumber: 131,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Projects\\\\ultimate-electrical-designer\\\\client\\\\src\\\\components\\\\auth\\\\RouteGuard.tsx\",\n            lineNumber: 126,\n            columnNumber: 7\n        }, this);\n    };\n}\n// Higher-order component for admin-only pages\nfunction withAdmin(Component, options = {}) {\n    return function AdminComponent(props) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RouteGuard, {\n            requireAuth: true,\n            requireAdmin: true,\n            redirectTo: options.redirectTo,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...props\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\ultimate-electrical-designer\\\\client\\\\src\\\\components\\\\auth\\\\RouteGuard.tsx\",\n                lineNumber: 149,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Projects\\\\ultimate-electrical-designer\\\\client\\\\src\\\\components\\\\auth\\\\RouteGuard.tsx\",\n            lineNumber: 144,\n            columnNumber: 7\n        }, this);\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/auth/RouteGuard.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)('inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50', {\n    variants: {\n        variant: {\n            default: 'bg-primary text-primary-foreground hover:bg-primary/90',\n            destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90',\n            outline: 'border border-input bg-background hover:bg-accent hover:text-accent-foreground',\n            secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',\n            ghost: 'hover:bg-accent hover:text-accent-foreground',\n            link: 'text-primary underline-offset-4 hover:underline',\n            primary: 'bg-blue-600 text-white hover:bg-blue-700',\n            success: 'bg-green-600 text-white hover:bg-green-700',\n            warning: 'bg-yellow-600 text-white hover:bg-yellow-700',\n            danger: 'bg-red-600 text-white hover:bg-red-700'\n        },\n        size: {\n            default: 'h-10 px-4 py-2',\n            sm: 'h-9 rounded-md px-3',\n            lg: 'h-11 rounded-md px-8',\n            xl: 'h-12 rounded-lg px-10 text-lg',\n            icon: 'h-10 w-10'\n        }\n    },\n    defaultVariants: {\n        variant: 'default',\n        size: 'default'\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\Projects\\\\ultimate-electrical-designer\\\\client\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 45,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = 'Button';\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/api/useAuth.ts":
/*!**********************************!*\
  !*** ./src/hooks/api/useAuth.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useChangePassword: () => (/* binding */ useChangePassword),\n/* harmony export */   useCurrentUser: () => (/* binding */ useCurrentUser),\n/* harmony export */   useLogin: () => (/* binding */ useLogin),\n/* harmony export */   useLogout: () => (/* binding */ useLogout),\n/* harmony export */   useUpdateProfile: () => (/* binding */ useUpdateProfile)\n/* harmony export */ });\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/useMutation.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/useQuery.js\");\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/api/client */ \"(ssr)/./src/lib/api/client.ts\");\n/* harmony import */ var _stores_authStore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/stores/authStore */ \"(ssr)/./src/stores/authStore.ts\");\n/* harmony import */ var _types_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/types/api */ \"(ssr)/./src/types/api.ts\");\n/* __next_internal_client_entry_do_not_use__ useLogin,useLogout,useChangePassword,useCurrentUser,useUpdateProfile auto */ /**\n * React Query hooks for authentication\n */ \n\n\n\n/**\n * Hook for user login\n */ function useLogin() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useQueryClient)();\n    const { setAuth, clearAuth } = (0,_stores_authStore__WEBPACK_IMPORTED_MODULE_1__.useAuthStore)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.useMutation)({\n        mutationKey: _types_api__WEBPACK_IMPORTED_MODULE_2__.MutationKeys.login,\n        mutationFn: {\n            \"useLogin.useMutation\": async (credentials)=>{\n                const response = await _lib_api_client__WEBPACK_IMPORTED_MODULE_0__.apiClient.login(credentials);\n                return response;\n            }\n        }[\"useLogin.useMutation\"],\n        onSuccess: {\n            \"useLogin.useMutation\": (data)=>{\n                // Set auth token in API client\n                _lib_api_client__WEBPACK_IMPORTED_MODULE_0__.apiClient.setAuthToken(data.access_token);\n                // Update auth store\n                setAuth(data.user, data.access_token);\n                // Invalidate and refetch user queries\n                queryClient.invalidateQueries({\n                    queryKey: _types_api__WEBPACK_IMPORTED_MODULE_2__.QueryKeys.currentUser\n                });\n                queryClient.setQueryData(_types_api__WEBPACK_IMPORTED_MODULE_2__.QueryKeys.currentUser, data.user);\n            }\n        }[\"useLogin.useMutation\"],\n        onError: {\n            \"useLogin.useMutation\": ()=>{\n                // Clear any existing auth state on login error\n                clearAuth();\n                _lib_api_client__WEBPACK_IMPORTED_MODULE_0__.apiClient.clearAuthToken();\n            }\n        }[\"useLogin.useMutation\"]\n    });\n}\n/**\n * Hook for user logout\n */ function useLogout() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useQueryClient)();\n    const { clearAuth } = (0,_stores_authStore__WEBPACK_IMPORTED_MODULE_1__.useAuthStore)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.useMutation)({\n        mutationKey: _types_api__WEBPACK_IMPORTED_MODULE_2__.MutationKeys.logout,\n        mutationFn: {\n            \"useLogout.useMutation\": async ()=>{\n                const response = await _lib_api_client__WEBPACK_IMPORTED_MODULE_0__.apiClient.logout();\n                return response;\n            }\n        }[\"useLogout.useMutation\"],\n        onSuccess: {\n            \"useLogout.useMutation\": ()=>{\n                // Clear auth state\n                clearAuth();\n                _lib_api_client__WEBPACK_IMPORTED_MODULE_0__.apiClient.clearAuthToken();\n                // Clear all cached data\n                queryClient.clear();\n            }\n        }[\"useLogout.useMutation\"],\n        onError: {\n            \"useLogout.useMutation\": ()=>{\n                // Even if logout fails on server, clear local state\n                clearAuth();\n                _lib_api_client__WEBPACK_IMPORTED_MODULE_0__.apiClient.clearAuthToken();\n                queryClient.clear();\n            }\n        }[\"useLogout.useMutation\"]\n    });\n}\n/**\n * Hook for password change\n */ function useChangePassword() {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.useMutation)({\n        mutationKey: _types_api__WEBPACK_IMPORTED_MODULE_2__.MutationKeys.changePassword,\n        mutationFn: {\n            \"useChangePassword.useMutation\": async (data)=>{\n                const response = await _lib_api_client__WEBPACK_IMPORTED_MODULE_0__.apiClient.changePassword(data);\n                return response;\n            }\n        }[\"useChangePassword.useMutation\"]\n    });\n}\n/**\n * Hook for getting current user\n */ function useCurrentUser() {\n    const { isAuthenticated } = (0,_stores_authStore__WEBPACK_IMPORTED_MODULE_1__.useAuthStore)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__.useQuery)({\n        queryKey: _types_api__WEBPACK_IMPORTED_MODULE_2__.QueryKeys.currentUser,\n        queryFn: {\n            \"useCurrentUser.useQuery\": async ()=>{\n                const response = await _lib_api_client__WEBPACK_IMPORTED_MODULE_0__.apiClient.getCurrentUser();\n                return response;\n            }\n        }[\"useCurrentUser.useQuery\"],\n        enabled: isAuthenticated,\n        staleTime: 10 * 60 * 1000,\n        gcTime: 30 * 60 * 1000\n    });\n}\n/**\n * Hook for updating current user profile\n */ function useUpdateProfile() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useQueryClient)();\n    const { updateUser } = (0,_stores_authStore__WEBPACK_IMPORTED_MODULE_1__.useAuthStore)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.useMutation)({\n        mutationKey: _types_api__WEBPACK_IMPORTED_MODULE_2__.MutationKeys.updateProfile,\n        mutationFn: {\n            \"useUpdateProfile.useMutation\": async (data)=>{\n                const response = await _lib_api_client__WEBPACK_IMPORTED_MODULE_0__.apiClient.updateCurrentUser(data);\n                return response;\n            }\n        }[\"useUpdateProfile.useMutation\"],\n        onSuccess: {\n            \"useUpdateProfile.useMutation\": (data)=>{\n                // Update auth store\n                updateUser(data);\n                // Update cached user data\n                queryClient.setQueryData(_types_api__WEBPACK_IMPORTED_MODULE_2__.QueryKeys.currentUser, data);\n                queryClient.invalidateQueries({\n                    queryKey: _types_api__WEBPACK_IMPORTED_MODULE_2__.QueryKeys.users\n                });\n            }\n        }[\"useUpdateProfile.useMutation\"]\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/api/useAuth.ts\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useAuth.ts":
/*!******************************!*\
  !*** ./src/hooks/useAuth.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _stores_authStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/stores/authStore */ \"(ssr)/./src/stores/authStore.ts\");\n/* harmony import */ var _hooks_api_useAuth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/api/useAuth */ \"(ssr)/./src/hooks/api/useAuth.ts\");\n/* harmony import */ var _lib_auth_tokenManager__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/auth/tokenManager */ \"(ssr)/./src/lib/auth/tokenManager.ts\");\n/* __next_internal_client_entry_do_not_use__ useAuth auto */ /**\n * Main authentication hook that combines Zustand store and React Query\n */ \n\n\n\n\nfunction useAuth() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const { user, token, isAuthenticated, isLoading, setAuth, clearAuth, setLoading, initializeAuth } = (0,_stores_authStore__WEBPACK_IMPORTED_MODULE_2__.useAuthStore)();\n    // React Query hooks\n    const loginMutation = (0,_hooks_api_useAuth__WEBPACK_IMPORTED_MODULE_3__.useLogin)();\n    const logoutMutation = (0,_hooks_api_useAuth__WEBPACK_IMPORTED_MODULE_3__.useLogout)();\n    const { data: currentUser, isLoading: isUserLoading, error: userError } = (0,_hooks_api_useAuth__WEBPACK_IMPORTED_MODULE_3__.useCurrentUser)();\n    // Initialize auth on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useAuth.useEffect\": ()=>{\n            initializeAuth();\n            _lib_auth_tokenManager__WEBPACK_IMPORTED_MODULE_4__.TokenManager.initializeTokens();\n        }\n    }[\"useAuth.useEffect\"], [\n        initializeAuth\n    ]);\n    // Sync current user data with store\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useAuth.useEffect\": ()=>{\n            if (currentUser && isAuthenticated) {\n                // Update user data in store if it's different\n                if (JSON.stringify(user) !== JSON.stringify(currentUser)) {\n                    _stores_authStore__WEBPACK_IMPORTED_MODULE_2__.useAuthStore.getState().updateUser(currentUser);\n                }\n            }\n        }\n    }[\"useAuth.useEffect\"], [\n        currentUser,\n        isAuthenticated,\n        user\n    ]);\n    // Handle user fetch errors (token might be invalid)\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useAuth.useEffect\": ()=>{\n            if (userError && isAuthenticated) {\n                // If we get an auth error while supposedly authenticated, clear auth\n                console.warn('Authentication error, clearing auth state:', userError);\n                clearAuth();\n                _lib_auth_tokenManager__WEBPACK_IMPORTED_MODULE_4__.TokenManager.clearTokens();\n            }\n        }\n    }[\"useAuth.useEffect\"], [\n        userError,\n        isAuthenticated,\n        clearAuth\n    ]);\n    /**\n   * Login function\n   */ const login = async (credentials)=>{\n        setLoading(true);\n        try {\n            const response = await loginMutation.mutateAsync(credentials);\n            // Store tokens\n            _lib_auth_tokenManager__WEBPACK_IMPORTED_MODULE_4__.TokenManager.setAccessToken(response.access_token);\n            // Update auth state\n            setAuth(response.user, response.access_token);\n            return response;\n        } catch (error) {\n            clearAuth();\n            _lib_auth_tokenManager__WEBPACK_IMPORTED_MODULE_4__.TokenManager.clearTokens();\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    /**\n   * Logout function\n   */ const logout = async ()=>{\n        setLoading(true);\n        try {\n            await logoutMutation.mutateAsync();\n        } catch (error) {\n            // Even if server logout fails, clear local state\n            console.warn('Server logout failed, clearing local state:', error);\n        } finally{\n            clearAuth();\n            _lib_auth_tokenManager__WEBPACK_IMPORTED_MODULE_4__.TokenManager.clearTokens();\n            setLoading(false);\n            router.push('/login');\n        }\n    };\n    /**\n   * Check if user has specific role\n   */ const hasRole = (role)=>{\n        return user?.role === role;\n    };\n    /**\n   * Check if user is admin\n   */ const isAdmin = ()=>{\n        return user?.is_admin === true || user?.role === 'ADMIN';\n    };\n    /**\n   * Require authentication (redirect to login if not authenticated)\n   */ const requireAuth = ()=>{\n        if (!isAuthenticated && !isLoading) {\n            router.push('/login');\n            return false;\n        }\n        return true;\n    };\n    /**\n   * Require admin role (redirect if not admin)\n   */ const requireAdmin = ()=>{\n        if (!requireAuth()) {\n            return false;\n        }\n        if (!isAdmin()) {\n            router.push('/dashboard') // Redirect to dashboard instead of login\n            ;\n            return false;\n        }\n        return true;\n    };\n    return {\n        // State\n        user,\n        token,\n        isAuthenticated,\n        isLoading: isLoading || isUserLoading || loginMutation.isPending || logoutMutation.isPending,\n        // Actions\n        login,\n        logout,\n        // Utilities\n        hasRole,\n        isAdmin,\n        requireAuth,\n        requireAdmin,\n        // Mutation states\n        loginError: loginMutation.error,\n        logoutError: logoutMutation.error,\n        isLoginPending: loginMutation.isPending,\n        isLogoutPending: logoutMutation.isPending\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useAuth.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/api/client.ts":
/*!*******************************!*\
  !*** ./src/lib/api/client.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiClient: () => (/* binding */ ApiClient),\n/* harmony export */   apiClient: () => (/* binding */ apiClient),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/**\n * API Client for Ultimate Electrical Designer\n * Provides type-safe HTTP client with authentication support\n */ class ApiClient {\n    constructor(config){\n        this.authToken = '';\n        this.baseURL = config.baseURL.replace(/\\/$/, '') // Remove trailing slash\n        ;\n        this.timeout = config.timeout || 30000;\n    }\n    /**\n   * Set authentication token for subsequent requests\n   */ setAuthToken(token) {\n        this.authToken = token;\n    }\n    /**\n   * Clear authentication token\n   */ clearAuthToken() {\n        this.authToken = '';\n    }\n    /**\n   * Get default headers for requests\n   */ getDefaultHeaders() {\n        const headers = {\n            'Content-Type': 'application/json'\n        };\n        if (this.authToken) {\n            headers.Authorization = `Bearer ${this.authToken}`;\n        }\n        return headers;\n    }\n    /**\n   * Make HTTP request with error handling\n   */ async request(endpoint, options = {}) {\n        const url = `${this.baseURL}${endpoint}`;\n        const { headers: customHeaders, timeout, signal, ...fetchOptions } = options;\n        const headers = {\n            ...this.getDefaultHeaders(),\n            ...customHeaders\n        };\n        const controller = new AbortController();\n        const timeoutId = setTimeout(()=>controller.abort(), timeout || this.timeout);\n        try {\n            const response = await fetch(url, {\n                ...fetchOptions,\n                headers,\n                signal: signal || controller.signal\n            });\n            clearTimeout(timeoutId);\n            const data = await response.json();\n            if (!response.ok) {\n                return {\n                    error: data,\n                    status: response.status\n                };\n            }\n            return {\n                data,\n                status: response.status\n            };\n        } catch (error) {\n            clearTimeout(timeoutId);\n            if (error instanceof Error) {\n                return {\n                    error: {\n                        detail: error.message\n                    },\n                    status: 0\n                };\n            }\n            return {\n                error: {\n                    detail: 'Unknown error occurred'\n                },\n                status: 0\n            };\n        }\n    }\n    /**\n   * GET request\n   */ async get(endpoint, options) {\n        return this.request(endpoint, {\n            method: 'GET',\n            ...options\n        });\n    }\n    /**\n   * POST request\n   */ async post(endpoint, data, options) {\n        return this.request(endpoint, {\n            method: 'POST',\n            body: data ? JSON.stringify(data) : undefined,\n            ...options\n        });\n    }\n    /**\n   * PUT request\n   */ async put(endpoint, data, options) {\n        return this.request(endpoint, {\n            method: 'PUT',\n            body: data ? JSON.stringify(data) : undefined,\n            ...options\n        });\n    }\n    /**\n   * DELETE request\n   */ async delete(endpoint, options) {\n        return this.request(endpoint, {\n            method: 'DELETE',\n            ...options\n        });\n    }\n    // Authentication endpoints\n    async login(credentials) {\n        const response = await this.post('/api/v1/auth/login', credentials);\n        if (response.error) {\n            throw new Error(response.error.detail);\n        }\n        return response.data;\n    }\n    async logout() {\n        const response = await this.post('/api/v1/auth/logout');\n        if (response.error) {\n            throw new Error(response.error.detail);\n        }\n        return response.data;\n    }\n    async changePassword(data) {\n        const response = await this.post('/api/v1/auth/change-password', data);\n        if (response.error) {\n            throw new Error(response.error.detail);\n        }\n        return response.data;\n    }\n    // User endpoints\n    async getCurrentUser() {\n        const response = await this.get('/api/v1/users/me');\n        if (response.error) {\n            throw new Error(response.error.detail);\n        }\n        return response.data;\n    }\n    async updateCurrentUser(data) {\n        const response = await this.put('/api/v1/users/me', data);\n        if (response.error) {\n            throw new Error(response.error.detail);\n        }\n        return response.data;\n    }\n    async getUsers(params) {\n        const searchParams = new URLSearchParams();\n        if (params?.skip) searchParams.set('skip', params.skip.toString());\n        if (params?.limit) searchParams.set('limit', params.limit.toString());\n        if (params?.search) searchParams.set('search', params.search);\n        if (params?.sort_by) searchParams.set('sort_by', params.sort_by);\n        if (params?.sort_order) searchParams.set('sort_order', params.sort_order);\n        const query = searchParams.toString();\n        const endpoint = `/api/v1/users${query ? `?${query}` : ''}`;\n        const response = await this.get(endpoint);\n        if (response.error) {\n            throw new Error(response.error.detail);\n        }\n        return response.data;\n    }\n    async getUsersSummary(limit) {\n        const endpoint = `/api/v1/users/summary${limit ? `?limit=${limit}` : ''}`;\n        const response = await this.get(endpoint);\n        if (response.error) {\n            throw new Error(response.error.detail);\n        }\n        return response.data;\n    }\n    async getUser(id) {\n        const response = await this.get(`/api/v1/users/${id}`);\n        if (response.error) {\n            throw new Error(response.error.detail);\n        }\n        return response.data;\n    }\n    async createUser(data) {\n        const response = await this.post('/api/v1/users', data);\n        if (response.error) {\n            throw new Error(response.error.detail);\n        }\n        return response.data;\n    }\n    async updateUser(id, data) {\n        const response = await this.put(`/api/v1/users/${id}`, data);\n        if (response.error) {\n            throw new Error(response.error.detail);\n        }\n        return response.data;\n    }\n    async deleteUser(id) {\n        const response = await this.delete(`/api/v1/users/${id}`);\n        if (response.error) {\n            throw new Error(response.error.detail);\n        }\n    }\n    // Health check endpoint\n    async healthCheck() {\n        const response = await this.get('/api/v1/health');\n        if (response.error) {\n            throw new Error(response.error.detail);\n        }\n        return response.data;\n    }\n}\n// Create singleton instance\nconst apiClient = new ApiClient({\n    baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (apiClient);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api/client.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/auth/tokenManager.ts":
/*!**************************************!*\
  !*** ./src/lib/auth/tokenManager.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TokenManager: () => (/* binding */ TokenManager)\n/* harmony export */ });\n/* harmony import */ var _stores_authStore__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/stores/authStore */ \"(ssr)/./src/stores/authStore.ts\");\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/api/client */ \"(ssr)/./src/lib/api/client.ts\");\n/**\n * JWT Token Management Utilities\n */ \n\nclass TokenManager {\n    static{\n        this.ACCESS_TOKEN_KEY = 'access_token';\n    }\n    static{\n        this.REFRESH_TOKEN_KEY = 'refresh_token';\n    }\n    /**\n   * Set access token in localStorage and API client\n   */ static setAccessToken(token) {\n        if (false) {}\n        _lib_api_client__WEBPACK_IMPORTED_MODULE_1__.apiClient.setAuthToken(token);\n    }\n    /**\n   * Get access token from localStorage\n   */ static getAccessToken() {\n        if (false) {}\n        return null;\n    }\n    /**\n   * Set refresh token in localStorage\n   */ static setRefreshToken(token) {\n        if (false) {}\n    }\n    /**\n   * Get refresh token from localStorage\n   */ static getRefreshToken() {\n        if (false) {}\n        return null;\n    }\n    /**\n   * Clear all tokens from localStorage and API client\n   */ static clearTokens() {\n        if (false) {}\n        _lib_api_client__WEBPACK_IMPORTED_MODULE_1__.apiClient.clearAuthToken();\n    }\n    /**\n   * Check if access token exists\n   */ static hasAccessToken() {\n        return !!this.getAccessToken();\n    }\n    /**\n   * Check if refresh token exists\n   */ static hasRefreshToken() {\n        return !!this.getRefreshToken();\n    }\n    /**\n   * Decode JWT token payload (without verification)\n   * Note: This is for client-side use only, server should always verify\n   */ static decodeToken(token) {\n        try {\n            const base64Url = token.split('.')[1];\n            const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');\n            const jsonPayload = decodeURIComponent(atob(base64).split('').map((c)=>'%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2)).join(''));\n            return JSON.parse(jsonPayload);\n        } catch (error) {\n            console.error('Error decoding token:', error);\n            return null;\n        }\n    }\n    /**\n   * Check if token is expired (client-side check only)\n   */ static isTokenExpired(token) {\n        try {\n            const payload = this.decodeToken(token);\n            if (!payload || !payload.exp) {\n                return true;\n            }\n            const currentTime = Math.floor(Date.now() / 1000);\n            return payload.exp < currentTime;\n        } catch (error) {\n            console.error('Error checking token expiration:', error);\n            return true;\n        }\n    }\n    /**\n   * Get token expiration time\n   */ static getTokenExpiration(token) {\n        try {\n            const payload = this.decodeToken(token);\n            if (!payload || !payload.exp) {\n                return null;\n            }\n            return new Date(payload.exp * 1000);\n        } catch (error) {\n            console.error('Error getting token expiration:', error);\n            return null;\n        }\n    }\n    /**\n   * Initialize tokens on app startup\n   */ static initializeTokens() {\n        const accessToken = this.getAccessToken();\n        if (accessToken && !this.isTokenExpired(accessToken)) {\n            // Set token in API client\n            _lib_api_client__WEBPACK_IMPORTED_MODULE_1__.apiClient.setAuthToken(accessToken);\n        } else {\n            // Clear expired or invalid tokens\n            this.clearTokens();\n            _stores_authStore__WEBPACK_IMPORTED_MODULE_0__.useAuthStore.getState().clearAuth();\n        }\n    }\n    /**\n   * Auto-refresh token before expiration\n   * Note: This would require a refresh endpoint on the backend\n   */ static async refreshTokenIfNeeded() {\n        const accessToken = this.getAccessToken();\n        const refreshToken = this.getRefreshToken();\n        if (!accessToken || !refreshToken) {\n            return false;\n        }\n        // Check if token expires in the next 5 minutes\n        const payload = this.decodeToken(accessToken);\n        if (!payload || !payload.exp) {\n            return false;\n        }\n        const currentTime = Math.floor(Date.now() / 1000);\n        const expirationTime = payload.exp;\n        const timeUntilExpiration = expirationTime - currentTime;\n        const fiveMinutes = 5 * 60;\n        if (timeUntilExpiration > fiveMinutes) {\n            return true // Token is still valid\n            ;\n        }\n        // TODO: Implement refresh token logic when backend supports it\n        // For now, just clear tokens if they're about to expire\n        if (timeUntilExpiration <= 0) {\n            this.clearTokens();\n            _stores_authStore__WEBPACK_IMPORTED_MODULE_0__.useAuthStore.getState().clearAuth();\n            return false;\n        }\n        return true;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/auth/tokenManager.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/react-query.tsx":
/*!*********************************!*\
  !*** ./src/lib/react-query.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ReactQueryProvider: () => (/* binding */ ReactQueryProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/queryClient.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _tanstack_react_query_devtools__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tanstack/react-query-devtools */ \"(ssr)/./node_modules/@tanstack/react-query-devtools/build/modern/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ReactQueryProvider auto */ \n/**\n * React Query configuration and provider setup\n */ \n\n\nfunction ReactQueryProvider({ children }) {\n    const [queryClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"ReactQueryProvider.useState\": ()=>new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.QueryClient({\n                defaultOptions: {\n                    queries: {\n                        // Stale time: 5 minutes\n                        staleTime: 5 * 60 * 1000,\n                        // Cache time: 10 minutes\n                        gcTime: 10 * 60 * 1000,\n                        // Retry failed requests 3 times\n                        retry: 3,\n                        // Retry delay increases exponentially\n                        retryDelay: {\n                            \"ReactQueryProvider.useState\": (attemptIndex)=>Math.min(1000 * 2 ** attemptIndex, 30000)\n                        }[\"ReactQueryProvider.useState\"],\n                        // Refetch on window focus in production\n                        refetchOnWindowFocus: \"development\" === 'production',\n                        // Don't refetch on reconnect by default\n                        refetchOnReconnect: false\n                    },\n                    mutations: {\n                        // Retry failed mutations once\n                        retry: 1,\n                        // Retry delay for mutations\n                        retryDelay: 1000\n                    }\n                }\n            })\n    }[\"ReactQueryProvider.useState\"]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.QueryClientProvider, {\n        client: queryClient,\n        children: [\n            children,\n             true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query_devtools__WEBPACK_IMPORTED_MODULE_4__.ReactQueryDevtools, {\n                initialIsOpen: false\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\ultimate-electrical-designer\\\\client\\\\src\\\\lib\\\\react-query.tsx\",\n                lineNumber: 48,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Projects\\\\ultimate-electrical-designer\\\\client\\\\src\\\\lib\\\\react-query.tsx\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/react-query.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE0QztBQUNKO0FBRWpDLFNBQVNFLEdBQUcsR0FBR0MsTUFBb0I7SUFDeEMsT0FBT0YsdURBQU9BLENBQUNELDBDQUFJQSxDQUFDRztBQUN0QiIsInNvdXJjZXMiOlsiRDpcXFByb2plY3RzXFx1bHRpbWF0ZS1lbGVjdHJpY2FsLWRlc2lnbmVyXFxjbGllbnRcXHNyY1xcbGliXFx1dGlscy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB0eXBlIENsYXNzVmFsdWUsIGNsc3ggfSBmcm9tICdjbHN4J1xuaW1wb3J0IHsgdHdNZXJnZSB9IGZyb20gJ3RhaWx3aW5kLW1lcmdlJ1xuXG5leHBvcnQgZnVuY3Rpb24gY24oLi4uaW5wdXRzOiBDbGFzc1ZhbHVlW10pIHtcbiAgcmV0dXJuIHR3TWVyZ2UoY2xzeChpbnB1dHMpKVxufVxuIl0sIm5hbWVzIjpbImNsc3giLCJ0d01lcmdlIiwiY24iLCJpbnB1dHMiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./src/stores/authStore.ts":
/*!*********************************!*\
  !*** ./src/stores/authStore.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAuthToken: () => (/* binding */ getAuthToken),\n/* harmony export */   getAuthUser: () => (/* binding */ getAuthUser),\n/* harmony export */   isUserAuthenticated: () => (/* binding */ isUserAuthenticated),\n/* harmony export */   useAuthStore: () => (/* binding */ useAuthStore),\n/* harmony export */   useAuthToken: () => (/* binding */ useAuthToken),\n/* harmony export */   useAuthUser: () => (/* binding */ useAuthUser),\n/* harmony export */   useIsAuthenticated: () => (/* binding */ useIsAuthenticated),\n/* harmony export */   useIsLoading: () => (/* binding */ useIsLoading)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/index.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand/middleware */ \"(ssr)/./node_modules/zustand/esm/middleware.mjs\");\n/* __next_internal_client_entry_do_not_use__ useAuthStore,useAuthUser,useAuthToken,useIsAuthenticated,useIsLoading,getAuthToken,getAuthUser,isUserAuthenticated auto */ /**\n * Zustand store for authentication state management\n */ \n\nconst useAuthStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.persist)((set, get)=>({\n        // Initial state\n        user: null,\n        token: null,\n        isAuthenticated: false,\n        isLoading: false,\n        // Set authentication data after successful login\n        setAuth: (user, token)=>{\n            set({\n                user,\n                token,\n                isAuthenticated: true,\n                isLoading: false\n            });\n        },\n        // Update user data (for profile updates)\n        updateUser: (user)=>{\n            set((state)=>({\n                    ...state,\n                    user\n                }));\n        },\n        // Clear authentication data on logout\n        clearAuth: ()=>{\n            set({\n                user: null,\n                token: null,\n                isAuthenticated: false,\n                isLoading: false\n            });\n        },\n        // Set loading state\n        setLoading: (loading)=>{\n            set((state)=>({\n                    ...state,\n                    isLoading: loading\n                }));\n        },\n        // Initialize auth state (called on app startup)\n        initializeAuth: ()=>{\n            const state = get();\n            if (state.token && state.user) {\n                // Token exists in storage, set as authenticated\n                set({\n                    isAuthenticated: true,\n                    isLoading: false\n                });\n            } else {\n                // No token, ensure clean state\n                set({\n                    user: null,\n                    token: null,\n                    isAuthenticated: false,\n                    isLoading: false\n                });\n            }\n        }\n    }), {\n    name: 'auth-storage',\n    storage: (0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.createJSONStorage)(()=>localStorage),\n    // Only persist user and token, not loading states\n    partialize: (state)=>({\n            user: state.user,\n            token: state.token\n        }),\n    // Rehydrate the state on app load\n    onRehydrateStorage: ()=>(state)=>{\n            if (state) {\n                state.initializeAuth();\n            }\n        }\n}));\n// Selectors for optimized component re-renders\nconst useAuthUser = ()=>useAuthStore({\n        \"useAuthUser.useAuthStore\": (state)=>state.user\n    }[\"useAuthUser.useAuthStore\"]);\nconst useAuthToken = ()=>useAuthStore({\n        \"useAuthToken.useAuthStore\": (state)=>state.token\n    }[\"useAuthToken.useAuthStore\"]);\nconst useIsAuthenticated = ()=>useAuthStore({\n        \"useIsAuthenticated.useAuthStore\": (state)=>state.isAuthenticated\n    }[\"useIsAuthenticated.useAuthStore\"]);\nconst useIsLoading = ()=>useAuthStore({\n        \"useIsLoading.useAuthStore\": (state)=>state.isLoading\n    }[\"useIsLoading.useAuthStore\"]);\n// Helper functions\nconst getAuthToken = ()=>useAuthStore.getState().token;\nconst getAuthUser = ()=>useAuthStore.getState().user;\nconst isUserAuthenticated = ()=>useAuthStore.getState().isAuthenticated;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/stores/authStore.ts\n");

/***/ }),

/***/ "(ssr)/./src/types/api.ts":
/*!**************************!*\
  !*** ./src/types/api.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MutationKeys: () => (/* binding */ MutationKeys),\n/* harmony export */   QueryKeys: () => (/* binding */ QueryKeys),\n/* harmony export */   UserRole: () => (/* binding */ UserRole)\n/* harmony export */ });\n/**\n * TypeScript type definitions for the Ultimate Electrical Designer API\n * Generated based on backend Pydantic schemas\n */ // Base types\n// User types\nvar UserRole = /*#__PURE__*/ function(UserRole) {\n    UserRole[\"VIEWER\"] = \"VIEWER\";\n    UserRole[\"EDITOR\"] = \"EDITOR\";\n    UserRole[\"ADMIN\"] = \"ADMIN\";\n    return UserRole;\n}({});\n// React Query keys\nconst QueryKeys = {\n    users: [\n        'users'\n    ],\n    usersList: (params)=>[\n            'users',\n            'list',\n            params\n        ],\n    user: (id)=>[\n            'users',\n            id\n        ],\n    currentUser: [\n        'users',\n        'me'\n    ],\n    usersSummary: [\n        'users',\n        'summary'\n    ],\n    health: [\n        'health'\n    ]\n};\n// Mutation keys\nconst MutationKeys = {\n    login: [\n        'auth',\n        'login'\n    ],\n    logout: [\n        'auth',\n        'logout'\n    ],\n    changePassword: [\n        'auth',\n        'changePassword'\n    ],\n    createUser: [\n        'users',\n        'create'\n    ],\n    updateUser: [\n        'users',\n        'update'\n    ],\n    deleteUser: [\n        'users',\n        'delete'\n    ],\n    updateProfile: [\n        'users',\n        'updateProfile'\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/types/api.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/@tanstack","vendor-chunks/next","vendor-chunks/tailwind-merge","vendor-chunks/zustand","vendor-chunks/use-sync-external-store","vendor-chunks/class-variance-authority","vendor-chunks/@swc","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F(auth)%2Flogin%2Fpage&page=%2F(auth)%2Flogin%2Fpage&appPaths=%2F(auth)%2Flogin%2Fpage&pagePath=private-next-app-dir%2F(auth)%2Flogin%2Fpage.tsx&appDir=D%3A%5CProjects%5Cultimate-electrical-designer%5Cclient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjects%5Cultimate-electrical-designer%5Cclient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();