{"version": "1.6.1", "results": [[":src/lib/api/__tests__/client.test.ts", {"duration": 18, "failed": false}], [":src/lib/auth/__tests__/tokenManager.test.ts", {"duration": 24, "failed": false}], [":src/components/auth/__tests__/RouteGuard.test.tsx", {"duration": 101, "failed": false}], [":src/hooks/__tests__/useAuth.test.tsx", {"duration": 60, "failed": false}], [":src/test/integration/auth-integration.test.tsx", {"duration": 423, "failed": false}], [":src/components/ui/__tests__/button.test.tsx", {"duration": 385, "failed": false}], [":src/components/auth/__tests__/LoginForm.test.tsx", {"duration": 1672, "failed": false}]]}